# Car Brands Table Documentation

## Overview

The `car_brands` table is a dedicated table for managing car brand information, replacing the previous `brand_names` array column in the `cars_register` table. This provides better normalization, easier management, and more flexibility for brand-related features.

## Table Structure

```sql
CREATE TABLE car_brands (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  slug VARCHAR(100) NOT NULL UNIQUE,
  logo_path VARCHAR(255),
  is_active BOOLEAN DEFAULT true,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Columns

| Column | Type | Description |
|--------|------|-------------|
| `id` | SERIAL | Primary key, auto-incrementing |
| `name` | VARCHAR(100) | Brand display name (e.g., "BMW", "Mercedes-Benz") |
| `slug` | VARCHAR(100) | URL-friendly identifier (e.g., "bmw", "mercedes-benz") |
| `logo_path` | VARCHAR(255) | Path to brand logo image |
| `is_active` | BOOLEAN | Whether brand is active/visible |
| `display_order` | INTEGER | Sort order for brand lists |
| `created_at` | TIMESTAMP | Record creation timestamp |
| `updated_at` | TIMESTAMP | Last update timestamp |

### Constraints

- `name` must be unique (case-insensitive check in API)
- `slug` must be unique
- `updated_at` automatically updated via trigger

## API Endpoints

### Public Endpoints

#### GET `/api/cars/brands`
Returns active car brands for use in forms and dropdowns.

**Response:**
```json
{
  "success": true,
  "data": {
    "brands": [
      {
        "id": 1,
        "name": "BMW",
        "slug": "bmw",
        "logoPath": "/cars/brands/dark/bmw.png",
        "isActive": true,
        "displayOrder": 6
      }
    ],
    "brandNames": ["BMW", "Mercedes-Benz", ...],
    "source": "database"
  }
}
```

### Admin Endpoints

#### GET `/api/admin/car-brands`
Returns all car brands (including inactive) for admin management.

**Query Parameters:**
- `includeInactive=true` - Include inactive brands

#### POST `/api/admin/car-brands`
Create a new car brand.

**Request Body:**
```json
{
  "name": "New Brand",
  "slug": "new-brand",
  "logoPath": "/cars/brands/dark/new-brand.png",
  "isActive": true,
  "displayOrder": 100
}
```

#### GET `/api/admin/car-brands/[id]`
Get specific brand by ID.

#### PUT `/api/admin/car-brands/[id]`
Update specific brand.

#### DELETE `/api/admin/car-brands/[id]`
Delete brand (only if not used by any cars).

#### PUT `/api/admin/car-brands`
Bulk update display order.

**Request Body:**
```json
{
  "brands": [
    {"id": 1, "displayOrder": 1},
    {"id": 2, "displayOrder": 2}
  ]
}
```

## Available Brands (59 total)

1. Acura
2. Alfa Romeo
3. Aston Martin
4. Audi
5. Bentley
6. BMW
7. Bugatti
8. Buick
9. Cadillac
10. Chevrolet
11. Chrysler
12. Citroen
13. Dacia
14. Daewoo
15. Dodge
16. Ferrari
17. Fiat
18. Ford
19. Genesis
20. GMC
21. Honda
22. Hummer
23. Hyundai
24. Infiniti
25. Isuzu
26. Jaguar
27. Jeep
28. Kia
29. Lamborghini
30. Land Rover
31. Lexus
32. Lincoln
33. Lotus
34. Maserati
35. Maybach
36. Mazda
37. McLaren
38. Mercedes
39. Mercedes-Benz
40. Mini
41. Mitsubishi
42. Nissan
43. Opel
44. Peugeot
45. Pontiac
46. Porsche
47. Ram
48. Renault
49. Rolls-Royce
50. Saab
51. Seat
52. Skoda
53. Smart
54. Subaru
55. Suzuki
56. Tesla
57. Toyota
58. Volkswagen
59. Volvo

## Logo Integration

Each brand has a corresponding logo file:
- **Dark logos**: `/cars/brands/dark/{slug}.png`
- **White logos**: `/cars/brands/white/{slug}.png`

The `logo_path` column stores the default (dark) logo path.

## Migration Notes

### Changes Made

1. **Removed** `brand_names` column from `cars_register` table
2. **Created** new `car_brands` table with proper normalization
3. **Updated** all API endpoints to use new table structure
4. **Maintained** backward compatibility in API responses

### Benefits

- **Better Performance**: No array operations, proper indexing
- **Easier Management**: CRUD operations for individual brands
- **More Flexible**: Can add metadata (logos, descriptions, etc.)
- **Scalable**: Easy to extend with additional brand properties
- **Maintainable**: Clear separation of concerns

## Usage Examples

### Frontend Hook
```typescript
import { useCarBrands } from '@/hooks/use-car-brands'

const { brands, loading, error } = useCarBrands()
```

### Direct API Call
```typescript
const response = await fetch('/api/cars/brands')
const { data } = await response.json()
const brandNames = data.brandNames
```

### Admin Management
```typescript
// Get all brands for admin
const response = await fetch('/api/admin/car-brands?includeInactive=true')

// Create new brand
await fetch('/api/admin/car-brands', {
  method: 'POST',
  body: JSON.stringify({ name: 'New Brand', slug: 'new-brand' })
})
```

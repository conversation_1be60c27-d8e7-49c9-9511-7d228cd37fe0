"use client"

import { useState, useEffect, useCallback } from 'react'
import { ClientsAPI, Client, Client<PERSON>ilters, CreateClientData, UpdateClientData } from '@/lib/clients'

export interface UseClientsReturn {
  clients: Client[]
  loading: boolean
  error: string | null
  pagination: {
    total: number
    limit: number
    offset: number
    hasMore: boolean
  }
  // Actions
  fetchClients: (filters?: ClientFilters) => Promise<void>
  createClient: (clientData: CreateClientData) => Promise<Client>
  updateClient: (id: number, clientData: UpdateClientData) => Promise<Client>
  deleteClient: (id: number) => Promise<void>
  refreshClients: () => Promise<void>
  // Filters
  setFilters: (filters: ClientFilters) => void
  filters: ClientFilters
}

export function useClients(initialFilters: ClientFilters = {}): UseClientsReturn {
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState<ClientFilters>(initialFilters)
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 50,
    offset: 0,
    hasMore: false
  })

  // Fetch clients with current filters
  const fetchClients = useCallback(async (newFilters?: ClientFilters) => {
    try {
      setLoading(true)
      setError(null)

      const filtersToUse = newFilters || filters
      const response = await ClientsAPI.getClients(filtersToUse)

      if (response.success) {
        setClients(response.data.clients)
        setPagination(response.data.pagination)
      } else {
        throw new Error('Failed to fetch clients')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      console.error('Error fetching clients:', err)
    } finally {
      setLoading(false)
    }
  }, []) // Remove filters dependency to prevent infinite loops

  // Create a new client
  const createClient = useCallback(async (clientData: CreateClientData): Promise<Client> => {
    try {
      setError(null)
      const response = await ClientsAPI.createClient(clientData)
      
      if (response.success) {
        // Add the new client to the beginning of the list
        setClients(prev => [response.data, ...prev])
        setPagination(prev => ({ ...prev, total: prev.total + 1 }))
        return response.data
      } else {
        throw new Error('Failed to create client')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create client'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }, [])

  // Update an existing client
  const updateClient = useCallback(async (id: number, clientData: UpdateClientData): Promise<Client> => {
    try {
      setError(null)
      console.log('Updating client with data:', { id, clientData })

      const response = await ClientsAPI.updateClient(id, clientData)
      console.log('Update response:', response)

      if (response.success) {
        // Update the client in the list
        setClients(prev =>
          prev.map(client =>
            client.id === id ? response.data : client
          )
        )
        return response.data
      } else {
        const errorMessage = response.error || 'Failed to update client'
        throw new Error(errorMessage)
      }
    } catch (err) {
      console.error('Error in updateClient:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to update client'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }, [])

  // Delete a client
  const deleteClient = useCallback(async (id: number): Promise<void> => {
    try {
      setError(null)
      await ClientsAPI.deleteClient(id)
      
      // Remove the client from the list
      setClients(prev => prev.filter(client => client.id !== id))
      setPagination(prev => ({ ...prev, total: prev.total - 1 }))
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete client'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }, [])

  // Refresh clients (re-fetch with current filters)
  const refreshClients = useCallback(async () => {
    await fetchClients()
  }, [fetchClients])

  // Update filters and fetch new data
  const updateFilters = useCallback((newFilters: ClientFilters) => {
    setFilters(newFilters)
    fetchClients(newFilters)
  }, [])

  // Initial fetch
  useEffect(() => {
    fetchClients()
  }, []) // Only run on mount

  return {
    clients,
    loading,
    error,
    pagination,
    fetchClients,
    createClient,
    updateClient,
    deleteClient,
    refreshClients,
    setFilters: updateFilters,
    filters
  }
}

// Hook for managing a single client
export function useClient(id: number) {
  const [client, setClient] = useState<Client | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchClient = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await ClientsAPI.getClient(id)
      
      if (response.success) {
        setClient(response.data)
      } else {
        throw new Error('Failed to fetch client')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      console.error('Error fetching client:', err)
    } finally {
      setLoading(false)
    }
  }, [id])

  useEffect(() => {
    if (id) {
      fetchClient()
    }
  }, [id, fetchClient])

  return {
    client,
    loading,
    error,
    refetch: fetchClient
  }
}

import { useState, useEffect, useCallback } from 'react'
import { CarsAPI, Car, CreateCarData, UpdateCarData, CarFilters } from '@/lib/cars'

interface UseCarsReturn {
  cars: Car[]
  loading: boolean
  error: string | null
  pagination: {
    total: number
    limit: number
    offset: number
    hasMore: boolean
  }
  
  // Actions
  fetchCars: (filters?: CarFilters) => Promise<void>
  createCar: (carData: CreateCarData) => Promise<Car>
  updateCar: (id: number, carData: UpdateCarData) => Promise<Car>
  deleteCar: (id: number) => Promise<void>
  getCar: (id: number) => Promise<Car>
  
  // Utility functions
  refreshCars: () => Promise<void>
  clearError: () => void
}

export function useCars(initialFilters?: CarFilters): UseCarsReturn {
  const [cars, setCars] = useState<Car[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 50,
    offset: 0,
    hasMore: false
  })
  const [currentFilters, setCurrentFilters] = useState<CarFilters>(initialFilters || {})

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const fetchCars = useCallback(async (filters?: CarFilters) => {
    try {
      setLoading(true)
      setError(null)

      const filtersToUse = filters || currentFilters
      if (filters) {
        setCurrentFilters(filtersToUse)
      }

      const response = await CarsAPI.getCars(filtersToUse)

      if (response.success) {
        setCars(response.data.cars)
        setPagination(response.data.pagination)
      } else {
        throw new Error('Failed to fetch cars')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch cars'
      setError(errorMessage)
      console.error('Error fetching cars:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  const createCar = useCallback(async (carData: CreateCarData): Promise<Car> => {
    try {
      setError(null)
      const response = await CarsAPI.createCar(carData)

      if (response.success) {
        // Refresh the entire list to get proper ordering
        fetchCars()
        return response.data
      } else {
        throw new Error('Failed to create car')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create car'
      setError(errorMessage)
      throw err
    }
  }, [fetchCars])

  const updateCar = useCallback(async (id: number, carData: UpdateCarData): Promise<Car> => {
    try {
      setError(null)
      const response = await CarsAPI.updateCar(id, carData)

      if (response.success) {
        // Refresh the entire list to get proper ordering (updated car should move to top)
        fetchCars()
        return response.data
      } else {
        throw new Error('Failed to update car')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update car'
      setError(errorMessage)
      throw err
    }
  }, [fetchCars])

  const deleteCar = useCallback(async (id: number): Promise<void> => {
    try {
      setError(null)
      const response = await CarsAPI.deleteCar(id)

      if (response.success) {
        // Refresh the entire list to get accurate count and ordering
        fetchCars()
      } else {
        throw new Error('Failed to delete car')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete car'
      setError(errorMessage)
      throw err
    }
  }, [fetchCars])

  const getCar = useCallback(async (id: number): Promise<Car> => {
    try {
      setError(null)
      const response = await CarsAPI.getCar(id)
      
      if (response.success) {
        return response.data
      } else {
        throw new Error('Failed to fetch car')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch car'
      setError(errorMessage)
      throw err
    }
  }, [])

  const refreshCars = useCallback(async () => {
    await fetchCars()
  }, [fetchCars])

  // Initial fetch on mount
  useEffect(() => {
    fetchCars()
  }, []) // Only run on mount

  return {
    cars,
    loading,
    error,
    pagination,
    fetchCars,
    createCar,
    updateCar,
    deleteCar,
    getCar,
    refreshCars,
    clearError
  }
}

// Hook for managing a single car (useful for edit forms)
export function useCar(id?: number) {
  const [car, setCar] = useState<Car | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchCar = useCallback(async (carId: number) => {
    try {
      setLoading(true)
      setError(null)
      const response = await CarsAPI.getCar(carId)
      
      if (response.success) {
        setCar(response.data)
      } else {
        throw new Error('Failed to fetch car')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch car'
      setError(errorMessage)
      console.error('Error fetching car:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    if (id) {
      fetchCar(id)
    }
  }, [id, fetchCar])

  return {
    car,
    loading,
    error,
    fetchCar,
    clearError: () => setError(null)
  }
}

import { useState, useEffect } from 'react'

interface Brand {
  id?: number
  name: string
  slug?: string
  logoPath?: string
  isActive?: boolean
  displayOrder?: number
}

interface BrandsResponse {
  success: boolean
  data: {
    brands: Brand[]
    brandNames: string[]
    source: 'database' | 'fallback'
  }
}

export function useCarBrands() {
  const [brands, setBrands] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchBrands = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const response = await fetch('/api/cars/brands')
        
        if (!response.ok) {
          throw new Error(`Failed to fetch brands: ${response.statusText}`)
        }
        
        const data: BrandsResponse = await response.json()
        
        if (data.success) {
          setBrands(data.data.brandNames)
        } else {
          throw new Error('Failed to fetch brands from API')
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch brands'
        setError(errorMessage)
        console.error('Error fetching car brands:', err)
        
        // Fallback to hardcoded brands if API fails
        setBrands([
          'Acura', 'Alfa Romeo', 'Aston Martin', 'Audi', 'Bentley', 'BMW', 'Bugatti', 'Buick',
          'Cadillac', 'Chevrolet', 'Chrysler', 'Citroen', 'Dacia', 'Daewoo', 'Dodge',
          'Ferrari', 'Fiat', 'Ford', 'Genesis', 'GMC', 'Honda', 'Hummer', 'Hyundai',
          'Infiniti', 'Isuzu', 'Jaguar', 'Jeep', 'Kia', 'Lamborghini', 'Land Rover',
          'Lexus', 'Lincoln', 'Lotus', 'Maserati', 'Maybach', 'Mazda', 'McLaren',
          'Mercedes', 'Mercedes-Benz', 'Mini', 'Mitsubishi', 'Nissan', 'Opel', 'Peugeot', 
          'Pontiac', 'Porsche', 'Ram', 'Renault', 'Rolls-Royce', 'Saab', 'Seat', 'Skoda',
          'Smart', 'Subaru', 'Suzuki', 'Tesla', 'Toyota', 'Volkswagen', 'Volvo'
        ])
      } finally {
        setLoading(false)
      }
    }

    fetchBrands()
  }, [])

  const refreshBrands = () => {
    fetchBrands()
  }

  return {
    brands,
    loading,
    error,
    clearError: () => setError(null),
    refreshBrands
  }
}

const { Pool } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'administrator',
  password: 'or_el_pass2025',
  database: 'rental_or_el_db',
  ssl: false
};

async function addSampleCars() {
  const pool = new Pool(dbConfig);
  
  try {
    console.log('🔌 Connecting to database...');
    const client = await pool.connect();
    
    // Sample cars data
    const sampleCars = [
      {
        plateNumber: 'ABC-123',
        brand: 'BMW',
        model: 'X5',
        year: 2023,
        vin: 'WBAFR9C50DD123456',
        registrationDate: '2023-01-15',
        status: 'active',
        notes: 'Luxury SUV with premium features'
      },
      {
        plateNumber: 'DEF-456',
        brand: 'Mercedes',
        model: 'C-Class',
        year: 2022,
        vin: 'WDD2050461F123456',
        registrationDate: '2022-06-20',
        status: 'rented',
        notes: 'Compact executive car'
      },
      {
        plateNumber: 'GHI-789',
        brand: 'Audi',
        model: 'A4',
        year: 2023,
        vin: 'WAUZZZ8K2DA123456',
        registrationDate: '2023-03-10',
        status: 'maintenance',
        notes: 'Currently in for scheduled maintenance'
      },
      {
        plateNumber: 'JKL-012',
        brand: 'Tesla',
        model: 'Model 3',
        year: 2023,
        vin: '5YJ3E1EA4KF123456',
        registrationDate: '2023-05-01',
        status: 'active',
        notes: 'Electric vehicle with autopilot'
      },
      {
        plateNumber: 'MNO-345',
        brand: 'Porsche',
        model: 'Cayenne',
        year: 2023,
        vin: 'WP1AB2A28KLA12345',
        registrationDate: '2023-02-14',
        status: 'active',
        notes: 'High-performance luxury SUV'
      }
    ];

    console.log('📝 Adding sample cars...');
    
    for (const car of sampleCars) {
      // Check if car already exists
      const existingCar = await client.query(
        'SELECT id FROM cars_register WHERE plate_number = $1',
        [car.plateNumber]
      );

      if (existingCar.rows.length > 0) {
        console.log(`⚠️  Car with plate ${car.plateNumber} already exists, skipping...`);
        continue;
      }

      const result = await client.query(`
        INSERT INTO cars_register (
          plate_number, brand, model, year, vin, 
          registration_date, status, notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id, plate_number, brand, model
      `, [
        car.plateNumber,
        car.brand,
        car.model,
        car.year,
        car.vin,
        car.registrationDate,
        car.status,
        car.notes
      ]);

      console.log(`✅ Added: ${result.rows[0].brand} ${result.rows[0].model} (${result.rows[0].plate_number})`);
    }

    // Show final count
    const count = await client.query('SELECT COUNT(*) as total FROM cars_register');
    console.log(`\n📊 Total cars in database: ${count.rows[0].total}`);
    
    client.release();
    await pool.end();
    
    console.log('\n🎉 Sample cars added successfully!');
    
  } catch (error) {
    console.error('❌ Error adding sample cars:', error.message);
    await pool.end();
  }
}

addSampleCars();

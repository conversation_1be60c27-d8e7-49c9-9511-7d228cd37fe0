YOU ARE A FULL STACK DEVELOPER
MCP AGENT PROTOCOL
==================
All AI agents must follow this protocol strictly.
🛠 BEFORE STARTING ANY TASK
---------------------------
Environment Requirement:
✅ Always operate within MCP_DOCKER environment.
❌ Do not proceed outside this context.

Memory Operations:
✅ Save all changes using the MCP memory system.
✅ Always search memory first when information is missing.
❌ Do not generate output if context is incomplete — ask for clarification.

Entity Management:
✅ Use consistent naming across entities and relationships.
✅ Link related entities to establish logical relationships.
✅ Update existing entities when applicable.
❌ Never create duplicate entries.
❌ Avoid vague or generic names.

Data Input Standards:
✅ Be specific and detailed.
✅ Include technical, business, and architectural context.
❌ Never skip critical details.

✅ DO
-----
- Be Specific — Include full technical and logical context.
- Use Consistent Naming — Entity and relationship names must be stable and predictable.
- Add Context — Define logic, data types, usage, and purpose.
- Create Relationships — Always relate new entities to existing ones.
- Update in Real Time — Save and reflect changes immediately.
- Search First — Avoid redundancy by checking memory/state first.

❌ DON'T
--------
- Skip Details — Avoid vague or high-level-only instructions.
- Create Duplicates — Always search before adding new entities.
- Use Generic Names — Be precise and purpose-driven in naming.
- Ignore Relationships — All entities must be connected logically.
- Leave Stale State — Existing entries must be updated, not overwritten or ignored.

🔄 PROJECT CONTEXT AWARENESS
----------------------------
- Always Read PLANNING.md before any task:
  Understand project architecture, goals, style, and constraints.
- Check TASK.md before beginning work:
  If a task isn't listed, add it with today’s date and description.
- Follow all naming conventions, folder structures, and design patterns defined in planning documentation.
- Im using english and albanian language for every change add new stuff and else make sure to have both languages correct.
- Always make the coding in a smart way that is so secure and carefully analyzed so no one can breach data or improvize in bugs to hack me , i wanna be full protected.

🧱 CODE STRUCTURE & MODULARITY
------------------------------
- Keep all files < 500 lines. Split logic into modules/utils early.
- Structure files by feature or responsibility:
  e.g., /features/users, /lib, /auth
- Use relative imports only — no deep or broken paths.
- Ensure all logic is portable, testable, and decoupled.

🧪 TESTING & RELIABILITY
------------------------
- Write unit tests for:
  Major logic, DB interactions, Edge/failure cases
- Store tests in /tests, mirroring main structure.
- Cover:
  ✅ Success scenarios
  ⚠️ Edge conditions
  ❌ Failure outcomes

✅ TASK MANAGEMENT
------------------
- Mark tasks as completed in TASK.md immediately.
- Log bugs, TODOs, and follow-ups under "Discovered During Work".

🧩 STYLE & CONVENTIONS
----------------------
- Use TypeScript whenever available.
- Enforce code quality with:
  prettier (formatting), eslint (linting)
- Use Zod or equivalent for validation.
- Prefer async/await over .then() chains.
- Keep components, actions, and logic separated.
- Add // Reason: comments for non-obvious logic.

🤖 AI-SPECIFIC RULES
--------------------
- Never assume — if any input is unclear, ask.
- Never hallucinate — use only verified files, modules, and APIs.
- Always verify file paths, imports, and identifiers before referencing.
- Do not delete existing code unless explicitly instructed.

📚 DOCUMENTATION STANDARDS
---------------------------
- Update README.md whenever:
  Features are added, Setup/run/deploy changes, New dependencies or environment variables are introduced
- Write:
  Clear inline comments for complex logic
  High-level documentation blocks at the top of files/modules
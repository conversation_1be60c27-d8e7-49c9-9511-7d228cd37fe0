import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - Fetch a specific car brand by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const brandId = parseInt(resolvedParams.id)

    if (isNaN(brandId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid brand ID' },
        { status: 400 }
      )
    }

    const sql = `
      SELECT 
        id,
        name,
        slug,
        logo_path as "logoPath",
        is_active as "isActive",
        display_order as "displayOrder",
        created_at as "createdAt",
        updated_at as "updatedAt"
      FROM car_brands
      WHERE id = $1
    `

    const result = await query(sql, [brandId])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Brand not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })

  } catch (error) {
    console.error('Admin car brand GET error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch car brand' },
      { status: 500 }
    )
  }
}

// PUT - Update a specific car brand
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const brandId = parseInt(resolvedParams.id)

    if (isNaN(brandId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid brand ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { name, slug, logoPath, isActive, displayOrder } = body

    // Check if brand exists
    const existingBrand = await query(
      'SELECT id FROM car_brands WHERE id = $1',
      [brandId]
    )

    if (existingBrand.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Brand not found' },
        { status: 404 }
      )
    }

    // Check if name is being changed and if it conflicts with another brand
    if (name) {
      const nameCheck = await query(
        'SELECT id FROM car_brands WHERE LOWER(name) = LOWER($1) AND id != $2',
        [name, brandId]
      )

      if (nameCheck.rows.length > 0) {
        return NextResponse.json(
          { success: false, error: 'A brand with this name already exists' },
          { status: 400 }
        )
      }
    }

    // Check if slug is being changed and if it conflicts with another brand
    if (slug) {
      const slugCheck = await query(
        'SELECT id FROM car_brands WHERE slug = $1 AND id != $2',
        [slug, brandId]
      )

      if (slugCheck.rows.length > 0) {
        return NextResponse.json(
          { success: false, error: 'A brand with this slug already exists' },
          { status: 400 }
        )
      }
    }

    // Build dynamic update query
    const updateFields: string[] = []
    const queryParams: any[] = []
    let paramIndex = 1

    if (name !== undefined) {
      updateFields.push(`name = $${paramIndex}`)
      queryParams.push(name)
      paramIndex++
    }

    if (slug !== undefined) {
      updateFields.push(`slug = $${paramIndex}`)
      queryParams.push(slug)
      paramIndex++
    }

    if (logoPath !== undefined) {
      updateFields.push(`logo_path = $${paramIndex}`)
      queryParams.push(logoPath)
      paramIndex++
    }

    if (isActive !== undefined) {
      updateFields.push(`is_active = $${paramIndex}`)
      queryParams.push(isActive)
      paramIndex++
    }

    if (displayOrder !== undefined) {
      updateFields.push(`display_order = $${paramIndex}`)
      queryParams.push(displayOrder)
      paramIndex++
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No fields to update' },
        { status: 400 }
      )
    }

    // Add updated_at field
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`)

    // Add brand ID as the last parameter
    queryParams.push(brandId)

    const sql = `
      UPDATE car_brands 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING 
        id,
        name,
        slug,
        logo_path as "logoPath",
        is_active as "isActive",
        display_order as "displayOrder",
        created_at as "createdAt",
        updated_at as "updatedAt"
    `

    const result = await query(sql, queryParams)

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })

  } catch (error) {
    console.error('Admin car brand PUT error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update car brand' },
      { status: 500 }
    )
  }
}

// DELETE - Delete a specific car brand
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const brandId = parseInt(resolvedParams.id)

    if (isNaN(brandId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid brand ID' },
        { status: 400 }
      )
    }

    // Check if brand exists
    const existingBrand = await query(
      'SELECT id, name FROM car_brands WHERE id = $1',
      [brandId]
    )

    if (existingBrand.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Brand not found' },
        { status: 404 }
      )
    }

    // Check if brand is being used by any cars
    const carsUsingBrand = await query(
      'SELECT COUNT(*) as count FROM cars_register WHERE LOWER(brand) = LOWER($1)',
      [existingBrand.rows[0].name]
    )

    if (parseInt(carsUsingBrand.rows[0].count) > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Cannot delete brand "${existingBrand.rows[0].name}" because it is being used by ${carsUsingBrand.rows[0].count} car(s). Please update or remove those cars first.` 
        },
        { status: 400 }
      )
    }

    // Delete the brand
    await query('DELETE FROM car_brands WHERE id = $1', [brandId])

    return NextResponse.json({
      success: true,
      message: `Brand "${existingBrand.rows[0].name}" has been deleted successfully`
    })

  } catch (error) {
    console.error('Admin car brand DELETE error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete car brand' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - Fetch all car brands (including inactive ones for admin)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeInactive = searchParams.get('includeInactive') === 'true'
    
    let sql = `
      SELECT 
        id,
        name,
        slug,
        logo_path as "logoPath",
        is_active as "isActive",
        display_order as "displayOrder",
        created_at as "createdAt",
        updated_at as "updatedAt"
      FROM car_brands
    `
    
    if (!includeInactive) {
      sql += ' WHERE is_active = true'
    }
    
    sql += ' ORDER BY display_order ASC, name ASC'
    
    const result = await query(sql)

    return NextResponse.json({
      success: true,
      data: {
        brands: result.rows,
        total: result.rows.length
      }
    })

  } catch (error) {
    console.error('Admin car brands GET error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch car brands' },
      { status: 500 }
    )
  }
}

// POST - Create a new car brand
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, slug, logoPath, isActive = true, displayOrder = 0 } = body

    // Validation
    if (!name) {
      return NextResponse.json(
        { success: false, error: 'Brand name is required' },
        { status: 400 }
      )
    }

    // Check if brand name already exists
    const existingBrand = await query(
      'SELECT id FROM car_brands WHERE LOWER(name) = LOWER($1)',
      [name]
    )

    if (existingBrand.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'A brand with this name already exists' },
        { status: 400 }
      )
    }

    // Generate slug if not provided
    const finalSlug = slug || name.toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '')

    // Check if slug already exists
    const existingSlug = await query(
      'SELECT id FROM car_brands WHERE slug = $1',
      [finalSlug]
    )

    if (existingSlug.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'A brand with this slug already exists' },
        { status: 400 }
      )
    }

    const sql = `
      INSERT INTO car_brands (name, slug, logo_path, is_active, display_order)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING 
        id,
        name,
        slug,
        logo_path as "logoPath",
        is_active as "isActive",
        display_order as "displayOrder",
        created_at as "createdAt",
        updated_at as "updatedAt"
    `

    const params = [name, finalSlug, logoPath, isActive, displayOrder]
    const result = await query(sql, params)

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })

  } catch (error) {
    console.error('Admin car brands POST error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create car brand' },
      { status: 500 }
    )
  }
}

// PUT - Update display order of multiple brands
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { brands } = body

    if (!Array.isArray(brands)) {
      return NextResponse.json(
        { success: false, error: 'Brands array is required' },
        { status: 400 }
      )
    }

    // Update display order for each brand
    for (const brand of brands) {
      if (brand.id && typeof brand.displayOrder === 'number') {
        await query(
          'UPDATE car_brands SET display_order = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
          [brand.displayOrder, brand.id]
        )
      }
    }

    return NextResponse.json({
      success: true,
      message: `Updated display order for ${brands.length} brands`
    })

  } catch (error) {
    console.error('Admin car brands PUT error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update brand order' },
      { status: 500 }
    )
  }
}

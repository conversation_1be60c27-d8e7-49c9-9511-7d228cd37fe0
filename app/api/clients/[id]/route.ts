import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - Fetch a specific client by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const clientId = parseInt(resolvedParams.id)

    if (isNaN(clientId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid client ID' },
        { status: 400 }
      )
    }

    const sql = `
      SELECT 
        id,
        full_name as name,
        email,
        phone,
        address,
        license_number as "licenseNumber",
        status,
        join_date as "joinDate",
        rating,
        preferred_cars as "preferredCars",
        avatar,
        total_bookings as "totalBookings",
        total_spent as "totalSpent",
        last_booking as "lastBooking",
        notes,
        created_at as "createdAt",
        updated_at as "updatedAt"
      FROM clients
      WHERE id = $1
    `

    const result = await query(sql, [clientId])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Client not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })

  } catch (error) {
    console.error('Error fetching client:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch client',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// PUT - Update a specific client
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const clientId = parseInt(resolvedParams.id)

    if (isNaN(clientId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid client ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const {
      name,
      email,
      phone,
      address,
      licenseNumber,
      status,
      rating,
      preferredCars,
      avatar,
      notes
    } = body

    // Build dynamic update query
    const updateFields: string[] = []
    const queryParams: any[] = []
    let paramIndex = 1

    if (name !== undefined) {
      updateFields.push(`full_name = $${paramIndex}`)
      queryParams.push(name)
      paramIndex++
    }

    if (email !== undefined) {
      updateFields.push(`email = $${paramIndex}`)
      queryParams.push(email)
      paramIndex++
    }

    if (phone !== undefined) {
      updateFields.push(`phone = $${paramIndex}`)
      queryParams.push(phone)
      paramIndex++
    }

    if (address !== undefined) {
      updateFields.push(`address = $${paramIndex}`)
      queryParams.push(address)
      paramIndex++
    }

    if (licenseNumber !== undefined) {
      updateFields.push(`license_number = $${paramIndex}`)
      queryParams.push(licenseNumber)
      paramIndex++
    }

    if (status !== undefined) {
      updateFields.push(`status = $${paramIndex}`)
      queryParams.push(status)
      paramIndex++
    }

    if (rating !== undefined) {
      updateFields.push(`rating = $${paramIndex}`)
      queryParams.push(rating)
      paramIndex++
    }

    if (preferredCars !== undefined) {
      updateFields.push(`preferred_cars = $${paramIndex}`)
      queryParams.push(JSON.stringify(preferredCars))
      paramIndex++
    }

    if (avatar !== undefined) {
      updateFields.push(`avatar = $${paramIndex}`)
      queryParams.push(avatar)
      paramIndex++
    }

    if (notes !== undefined) {
      updateFields.push(`notes = $${paramIndex}`)
      queryParams.push(notes)
      paramIndex++
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No fields to update' },
        { status: 400 }
      )
    }

    // Add updated_at
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`)

    // Add client ID as last parameter
    queryParams.push(clientId)

    const sql = `
      UPDATE clients
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING
        id,
        full_name as name,
        email,
        phone,
        address,
        license_number as "licenseNumber",
        status,
        join_date as "joinDate",
        rating,
        preferred_cars as "preferredCars",
        avatar,
        total_bookings as "totalBookings",
        total_spent as "totalSpent",
        last_booking as "lastBooking",
        notes,
        created_at as "createdAt",
        updated_at as "updatedAt"
    `

    const result = await query(sql, queryParams)

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Client not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Client updated successfully',
      data: result.rows[0]
    })

  } catch (error) {
    console.error('Error updating client:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update client',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// DELETE - Delete a specific client
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const clientId = parseInt(resolvedParams.id)

    if (isNaN(clientId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid client ID' },
        { status: 400 }
      )
    }

    // Check if client has active bookings
    const bookingCheck = await query(
      'SELECT COUNT(*) as count FROM active_bookings WHERE client_id = $1',
      [clientId]
    )

    if (parseInt(bookingCheck.rows[0].count) > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Cannot delete client with active bookings. Please complete or cancel all bookings first.' 
        },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM clients WHERE id = $1 RETURNING id, full_name as name',
      [clientId]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Client not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Client deleted successfully',
      data: result.rows[0]
    })

  } catch (error) {
    console.error('Error deleting client:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete client',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - Fetch all cars with optional filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const search = searchParams.get('search')
    const brand = searchParams.get('brand')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    let sql = `
      SELECT 
        id,
        plate_number as "plateNumber",
        brand,
        model,
        year,
        vin,
        registration_date as "registrationDate",
        status,
        notes,
        created_at as "createdAt",
        updated_at as "updatedAt"
      FROM cars_register
      WHERE 1=1
    `
    
    const queryParams: any[] = []
    let paramIndex = 1

    // Add filters
    if (status) {
      sql += ` AND status = $${paramIndex}`
      queryParams.push(status)
      paramIndex++
    }

    if (brand) {
      sql += ` AND LOWER(brand) = LOWER($${paramIndex})`
      queryParams.push(brand)
      paramIndex++
    }

    if (search) {
      sql += ` AND (
        LOWER(brand) LIKE LOWER($${paramIndex}) OR 
        LOWER(model) LIKE LOWER($${paramIndex}) OR 
        LOWER(plate_number) LIKE LOWER($${paramIndex}) OR
        LOWER(vin) LIKE LOWER($${paramIndex})
      )`
      queryParams.push(`%${search}%`)
      paramIndex++
    }

    // Add ordering and pagination
    sql += ` ORDER BY created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`
    queryParams.push(limit, offset)

    const result = await query(sql, queryParams)

    // Get total count for pagination
    let countSql = `SELECT COUNT(*) as total FROM cars_register WHERE 1=1`
    const countParams: any[] = []
    let countParamIndex = 1

    if (status) {
      countSql += ` AND status = $${countParamIndex}`
      countParams.push(status)
      countParamIndex++
    }

    if (brand) {
      countSql += ` AND LOWER(brand) = LOWER($${countParamIndex})`
      countParams.push(brand)
      countParamIndex++
    }

    if (search) {
      countSql += ` AND (
        LOWER(brand) LIKE LOWER($${countParamIndex}) OR 
        LOWER(model) LIKE LOWER($${countParamIndex}) OR 
        LOWER(plate_number) LIKE LOWER($${countParamIndex}) OR
        LOWER(vin) LIKE LOWER($${countParamIndex})
      )`
      countParams.push(`%${search}%`)
    }

    const countResult = await query(countSql, countParams)
    const total = parseInt(countResult.rows[0].total)

    return NextResponse.json({
      success: true,
      data: {
        cars: result.rows,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      }
    })

  } catch (error) {
    console.error('Cars GET error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch cars' },
      { status: 500 }
    )
  }
}

// POST - Create a new car
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      plateNumber,
      brand,
      model,
      year,
      vin,
      registrationDate,
      status = 'active',
      notes
    } = body

    // Validation
    if (!plateNumber) {
      return NextResponse.json(
        { success: false, error: 'Plate number is required' },
        { status: 400 }
      )
    }

    if (!brand) {
      return NextResponse.json(
        { success: false, error: 'Brand is required' },
        { status: 400 }
      )
    }

    // Check if plate number already exists
    const existingCar = await query(
      'SELECT id FROM cars_register WHERE plate_number = $1',
      [plateNumber]
    )

    if (existingCar.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'A car with this plate number already exists' },
        { status: 400 }
      )
    }

    const sql = `
      INSERT INTO cars_register (
        plate_number, brand, model, year, vin, 
        registration_date, status, notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING 
        id,
        plate_number as "plateNumber",
        brand,
        model,
        year,
        vin,
        registration_date as "registrationDate",
        status,
        notes,
        created_at as "createdAt",
        updated_at as "updatedAt"
    `

    const params = [
      plateNumber,
      brand,
      model,
      year,
      vin,
      registrationDate,
      status,
      notes
    ]

    const result = await query(sql, params)

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })

  } catch (error) {
    console.error('Cars POST error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create car' },
      { status: 500 }
    )
  }
}

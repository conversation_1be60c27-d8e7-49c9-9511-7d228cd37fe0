import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - Fetch a specific car by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const carId = parseInt(resolvedParams.id)

    if (isNaN(carId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid car ID' },
        { status: 400 }
      )
    }

    const sql = `
      SELECT
        c.id,
        c.plate_number as "plateNumber",
        c.brand,
        c.model,
        c.year,
        c.vin,
        c.registration_date as "registrationDate",
        c.status,
        c.notes,
        c.created_at as "createdAt",
        c.updated_at as "updatedAt",
        cb.id as "brandId",
        cb.name as "brandName",
        cb.slug as "brandSlug",
        cb.logo_path as "brandLogoPath",
        cb.is_active as "brandIsActive"
      FROM cars_register c
      LEFT JOIN car_brands cb ON LOWER(c.brand) = LOWER(cb.name)
      WHERE c.id = $1
    `

    const result = await query(sql, [carId])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Car not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })

  } catch (error) {
    console.error('Car GET error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch car' },
      { status: 500 }
    )
  }
}

// PUT - Update a specific car
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const carId = parseInt(resolvedParams.id)

    if (isNaN(carId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid car ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const {
      plateNumber,
      brand,
      model,
      year,
      vin,
      registrationDate,
      status,
      notes
    } = body

    // Check if car exists
    const existingCar = await query(
      'SELECT id FROM cars_register WHERE id = $1',
      [carId]
    )

    if (existingCar.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Car not found' },
        { status: 404 }
      )
    }

    // Check if plate number is being changed and if it conflicts with another car
    if (plateNumber) {
      const plateCheck = await query(
        'SELECT id FROM cars_register WHERE plate_number = $1 AND id != $2',
        [plateNumber, carId]
      )

      if (plateCheck.rows.length > 0) {
        return NextResponse.json(
          { success: false, error: 'A car with this plate number already exists' },
          { status: 400 }
        )
      }
    }

    // Build dynamic update query
    const updateFields: string[] = []
    const queryParams: any[] = []
    let paramIndex = 1

    if (plateNumber !== undefined) {
      updateFields.push(`plate_number = $${paramIndex}`)
      queryParams.push(plateNumber)
      paramIndex++
    }

    if (brand !== undefined) {
      updateFields.push(`brand = $${paramIndex}`)
      queryParams.push(brand)
      paramIndex++
    }

    if (model !== undefined) {
      updateFields.push(`model = $${paramIndex}`)
      queryParams.push(model)
      paramIndex++
    }

    if (year !== undefined) {
      updateFields.push(`year = $${paramIndex}`)
      queryParams.push(year)
      paramIndex++
    }

    if (vin !== undefined) {
      updateFields.push(`vin = $${paramIndex}`)
      queryParams.push(vin)
      paramIndex++
    }

    if (registrationDate !== undefined) {
      updateFields.push(`registration_date = $${paramIndex}`)
      queryParams.push(registrationDate)
      paramIndex++
    }

    if (status !== undefined) {
      updateFields.push(`status = $${paramIndex}`)
      queryParams.push(status)
      paramIndex++
    }

    if (notes !== undefined) {
      updateFields.push(`notes = $${paramIndex}`)
      queryParams.push(notes)
      paramIndex++
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No fields to update' },
        { status: 400 }
      )
    }

    // Add updated_at field
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`)

    // Add car ID as the last parameter
    queryParams.push(carId)

    // First update the car
    const updateSql = `
      UPDATE cars_register
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING id
    `

    await query(updateSql, queryParams)

    // Then fetch the updated car with brand data
    const fetchSql = `
      SELECT
        c.id,
        c.plate_number as "plateNumber",
        c.brand,
        c.model,
        c.year,
        c.vin,
        c.registration_date as "registrationDate",
        c.status,
        c.notes,
        c.created_at as "createdAt",
        c.updated_at as "updatedAt",
        cb.id as "brandId",
        cb.name as "brandName",
        cb.slug as "brandSlug",
        cb.logo_path as "brandLogoPath",
        cb.is_active as "brandIsActive"
      FROM cars_register c
      LEFT JOIN car_brands cb ON LOWER(c.brand) = LOWER(cb.name)
      WHERE c.id = $1
    `

    const result = await query(fetchSql, [carId])

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })

  } catch (error) {
    console.error('Car PUT error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update car' },
      { status: 500 }
    )
  }
}

// DELETE - Delete a specific car
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const carId = parseInt(resolvedParams.id)

    if (isNaN(carId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid car ID' },
        { status: 400 }
      )
    }

    // Check if car exists
    const existingCar = await query(
      'SELECT id, plate_number FROM cars_register WHERE id = $1',
      [carId]
    )

    if (existingCar.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Car not found' },
        { status: 404 }
      )
    }

    // TODO: Check if car has active bookings (when booking system is implemented)
    // const bookingCheck = await query(
    //   'SELECT COUNT(*) as count FROM active_bookings WHERE car_id = $1',
    //   [carId]
    // )
    // 
    // if (parseInt(bookingCheck.rows[0].count) > 0) {
    //   return NextResponse.json(
    //     { 
    //       success: false, 
    //       error: 'Cannot delete car with active bookings. Please complete or cancel all bookings first.' 
    //     },
    //     { status: 400 }
    //   )
    // }

    // Delete the car
    await query('DELETE FROM cars_register WHERE id = $1', [carId])

    return NextResponse.json({
      success: true,
      message: `Car with plate number ${existingCar.rows[0].plate_number} has been deleted successfully`
    })

  } catch (error) {
    console.error('Car DELETE error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete car' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - Fetch all available car brand names
export async function GET(request: NextRequest) {
  try {
    // Get brand names from car_brands table
    const sql = `
      SELECT
        id,
        name,
        slug,
        logo_path as "logoPath",
        is_active as "isActive",
        display_order as "displayOrder"
      FROM car_brands
      WHERE is_active = true
      ORDER BY display_order ASC, name ASC
    `

    const result = await query(sql)

    if (result.rows.length === 0) {
      // Fallback to hardcoded list if no brands exist
      const fallbackBrands = [
        'Acura', 'Alfa Romeo', 'Aston Martin', 'Audi', 'Bentley', 'BMW', 'Bugatti', 'Buick',
        'Cadillac', 'Chevrolet', 'Chrysler', 'Citroen', 'Dacia', 'Daewoo', 'Dodge',
        'Ferrari', 'Fiat', 'Ford', 'Genesis', 'GMC', 'Honda', 'Hummer', 'Hyundai',
        'Infiniti', 'Isuzu', 'Jaguar', 'Jeep', 'Kia', 'Lamborghini', 'Land Rover',
        'Lexus', 'Lincoln', 'Lotus', 'Maserati', 'Maybach', 'Mazda', 'McLaren',
        'Mercedes', 'Mercedes-Benz', 'Mini', 'Mitsubishi', 'Nissan', 'Opel', 'Peugeot',
        'Pontiac', 'Porsche', 'Ram', 'Renault', 'Rolls-Royce', 'Saab', 'Seat', 'Skoda',
        'Smart', 'Subaru', 'Suzuki', 'Tesla', 'Toyota', 'Volkswagen', 'Volvo'
      ]

      return NextResponse.json({
        success: true,
        data: {
          brands: fallbackBrands.map(name => ({ name })),
          brandNames: fallbackBrands,
          source: 'fallback'
        }
      })
    }

    // Extract just the names for backward compatibility
    const brandNames = result.rows.map(row => row.name)

    return NextResponse.json({
      success: true,
      data: {
        brands: result.rows,
        brandNames: brandNames,
        source: 'database'
      }
    })

  } catch (error) {
    console.error('Brands GET error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch car brands' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - Fetch all available car brand names
export async function GET(request: NextRequest) {
  try {
    // Get brand names from any car record (they're all the same)
    const sql = `
      SELECT brand_names as "brandNames"
      FROM cars_register 
      WHERE brand_names IS NOT NULL
      LIMIT 1
    `
    
    const result = await query(sql)

    if (result.rows.length === 0) {
      // Fallback to hardcoded list if no cars exist
      const fallbackBrands = [
        'Acura', 'Alfa Romeo', 'Aston Martin', 'Audi', 'Bentley', 'BMW', 'Bugatti', 'Buick',
        'Cadillac', 'Chevrolet', 'Chrysler', 'Citroen', 'Dacia', 'Daewoo', 'Dodge',
        'Ferrari', 'Fiat', 'Ford', 'Genesis', 'GMC', 'Honda', 'Hummer', 'Hyundai',
        'Infiniti', 'Isuzu', 'Jaguar', 'Jeep', 'Kia', 'Lamborghini', 'Land Rover',
        'Lexus', 'Lincoln', 'Lotus', 'Maserati', 'Maybach', 'Mazda', 'McLaren',
        'Mercedes', 'Mercedes-Benz', 'Mini', 'Mitsubishi', 'Nissan', 'Opel', 'Peugeot', 
        'Pontiac', 'Porsche', 'Ram', 'Renault', 'Rolls-Royce', 'Saab', 'Seat', 'Skoda',
        'Smart', 'Subaru', 'Suzuki', 'Tesla', 'Toyota', 'Volkswagen', 'Volvo'
      ]

      return NextResponse.json({
        success: true,
        data: {
          brands: fallbackBrands,
          source: 'fallback'
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        brands: result.rows[0].brandNames,
        source: 'database'
      }
    })

  } catch (error) {
    console.error('Brands GET error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch car brands' },
      { status: 500 }
    )
  }
}

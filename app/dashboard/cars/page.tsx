"use client"

import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { DashboardHeader } from "@/components/dashboard-header"
import { useLanguage } from "@/components/language-provider"
import { Plus, Search, Edit, Trash2, Fuel, Users, Calendar, Filter, Grid, List, Loader2, AlertTriangle, Tag } from "lucide-react"
import { useState, useEffect } from "react"
import { useCars } from "@/hooks/use-cars"
import { AddCarDialog } from "@/components/cars/add-car-dialog"
import { EditCarDialog } from "@/components/cars/edit-car-dialog"
import { BrandManagementDialog } from "@/components/cars/brand-management-dialog"
import { getCarStatusColor, getCarBrandLogo, Car } from "@/lib/cars"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertD<PERSON>ogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

// Helper function to get brand logo from car data
const getCarBrandLogoFromData = (car: Car, theme: 'dark' | 'white' = 'dark'): string => {
  // Prefer database brand logo path if available
  if (car.brandLogoPath) {
    return theme === 'white'
      ? car.brandLogoPath.replace('/dark/', '/white/')
      : car.brandLogoPath
  }

  // Fallback to helper function
  return getCarBrandLogo(car.brand, theme)
}

export default function CarsPage() {
  const { t } = useLanguage()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedBrand, setSelectedBrand] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [carToDelete, setCarToDelete] = useState<any>(null)
  const [deleting, setDeleting] = useState(false)

  // Use the cars hook
  const { cars, loading, error, fetchCars, deleteCar, clearError } = useCars()

  // Fetch cars on component mount
  useEffect(() => {
    fetchCars()
  }, [])



  // Get unique brands from cars data
  const brands = Array.from(new Set(cars.map(car => car.brand))).sort()

  // Filter cars based on search and filters
  const filteredCars = cars.filter((car) => {
    const matchesSearch =
      car.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (car.model && car.model.toLowerCase().includes(searchTerm.toLowerCase())) ||
      car.plateNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (car.vin && car.vin.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesBrand = selectedBrand === "all" || car.brand === selectedBrand
    const matchesStatus = selectedStatus === "all" || car.status === selectedStatus

    return matchesSearch && matchesBrand && matchesStatus
  })

  // Handle delete car
  const handleDeleteCar = async () => {
    if (!carToDelete) return

    try {
      setDeleting(true)
      await deleteCar(carToDelete.id)
      setDeleteDialogOpen(false)
      setCarToDelete(null)
    } catch (err) {
      console.error('Failed to delete car:', err)
    } finally {
      setDeleting(false)
    }
  }

  const openDeleteDialog = (car: any) => {
    setCarToDelete(car)
    setDeleteDialogOpen(true)
  }

  // Loading state
  if (loading && cars.length === 0) {
    return (
      <div className="flex flex-col min-h-screen">
        <DashboardHeader title={t("nav.cars")} subtitle="Compact fleet management" showActions={false} />
        <div className="flex-1 flex items-center justify-center">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading cars...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen">
      <DashboardHeader title={t("nav.cars")} subtitle="Compact fleet management" showActions={false} />

      <div className="flex-1 p-6 space-y-4 bg-gradient-to-br from-background to-muted/20">
        {/* Error Display */}
        {error && (
          <div className="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-md flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            <span>{error}</span>
            <Button variant="ghost" size="sm" onClick={clearError} className="ml-auto">
              Dismiss
            </Button>
          </div>
        )}

        {/* Brand Filters */}
        <div className="flex items-center gap-2 overflow-x-auto pb-2">
          <Button
            variant={selectedBrand === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedBrand("all")}
            className="whitespace-nowrap h-8 px-3 text-xs"
          >
            All Brands
          </Button>
          {brands.map((brand) => (
            <Button
              key={brand}
              variant={selectedBrand === brand ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedBrand(brand)}
              className="whitespace-nowrap h-8 px-2 flex items-center gap-2"
            >
              <img
                src={getCarBrandLogo(brand)}
                alt={brand}
                className="w-4 h-4 object-contain"
                onError={(e) => {
                  e.currentTarget.src = "/placeholder.svg"
                }}
              />
              <span className="text-xs">{brand}</span>
            </Button>
          ))}
        </div>

        {/* Status Filters */}
        <div className="flex items-center gap-2 overflow-x-auto pb-2">
          <Button
            variant={selectedStatus === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedStatus("all")}
            className="whitespace-nowrap h-7 px-2 text-xs"
          >
            All Status
          </Button>
          {['active', 'sold', 'maintenance', 'inactive'].map((status) => (
            <Button
              key={status}
              variant={selectedStatus === status ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedStatus(status)}
              className="whitespace-nowrap h-7 px-2 text-xs capitalize"
            >
              {status}
            </Button>
          ))}
        </div>



        {/* Controls */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-3">
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search cars..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-9 bg-muted/50 border-0"
              />
            </div>
            <div className="flex gap-1 justify-center sm:justify-start">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="h-9 px-3 flex-1 sm:flex-none"
              >
                <Grid className="h-4 w-4 sm:mr-0 mr-2" />
                <span className="sm:hidden">Grid</span>
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="h-9 px-3 flex-1 sm:flex-none"
              >
                <List className="h-4 w-4 sm:mr-0 mr-2" />
                <span className="sm:hidden">List</span>
              </Button>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="h-9 px-3 text-xs hidden sm:flex">
              <Filter className="h-4 w-4 mr-1" />
              Filter
            </Button>
            <BrandManagementDialog>
              <Button variant="outline" size="sm" className="h-9 px-3 text-xs">
                <Tag className="h-4 w-4 mr-1" />
                Add Brand
              </Button>
            </BrandManagementDialog>
            <AddCarDialog>
              <Button className="gradient-bg h-9 px-3 text-xs w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-1" />
                Add Car
              </Button>
            </AddCarDialog>
          </div>
        </div>

        {/* Cars Display */}
        {filteredCars.length === 0 && !loading ? (
          <div className="text-center py-12">
            <div className="text-muted-foreground">
              {cars.length === 0 ? "No cars found. Add your first car to get started." : "No cars match your current filters."}
            </div>
          </div>
        ) : viewMode === "grid" ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
            {filteredCars.map((car) => (
              <Card key={car.id} className="overflow-hidden card-hover border-0 shadow-sm">
                <div className="aspect-video relative bg-gradient-to-br from-muted/30 to-muted/60">
                  <Badge className={`absolute top-2 right-2 text-xs ${getCarStatusColor(car.status || 'active')}`}>
                    {car.status || 'active'}
                  </Badge>
                  <div className="absolute top-2 left-2 w-8 h-8 bg-white rounded-lg p-1 shadow-sm">
                    <img
                      src={getCarBrandLogoFromData(car)}
                      alt={car.brand}
                      className="w-full h-full object-contain"
                      onError={(e) => {
                        e.currentTarget.src = "/placeholder.svg"
                      }}
                    />
                  </div>
                  <div className="absolute bottom-2 left-2 right-2">
                    <h3 className="font-bold text-sm text-white drop-shadow-lg">
                      {car.brand} {car.model || ''}
                    </h3>
                    <p className="text-xs text-white/80 drop-shadow">
                      {car.year || 'N/A'} • {car.plateNumber}
                    </p>
                  </div>
                </div>
                <CardContent className="p-3">
                  <div className="space-y-2">
                    <div className="grid grid-cols-2 gap-1 text-xs">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        <span>{car.registrationDate ? new Date(car.registrationDate).getFullYear() : 'N/A'}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <span className="text-muted-foreground">VIN:</span>
                        <span className="truncate">{car.vin || 'N/A'}</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{car.plateNumber}</span>
                      <div className="flex gap-1">
                        <EditCarDialog car={car}>
                          <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                            <Edit className="h-3 w-3" />
                          </Button>
                        </EditCarDialog>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() => openDeleteDialog(car)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card className="border-0 shadow-lg">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/30">
                      <th className="text-left p-3 text-xs font-medium text-muted-foreground">CAR</th>
                      <th className="text-left p-3 text-xs font-medium text-muted-foreground">DETAILS</th>
                      <th className="text-left p-3 text-xs font-medium text-muted-foreground">STATUS</th>
                      <th className="text-left p-3 text-xs font-medium text-muted-foreground">REGISTRATION</th>
                      <th className="text-left p-3 text-xs font-medium text-muted-foreground">ACTIONS</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredCars.map((car, index) => (
                      <tr
                        key={car.id}
                        className={`border-b hover:bg-muted/20 transition-colors ${
                          index % 2 === 0 ? "bg-background" : "bg-muted/10"
                        }`}
                      >
                        <td className="p-3">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-white rounded-lg p-1 shadow-sm">
                              <img
                                src={getCarBrandLogoFromData(car)}
                                alt={car.brand}
                                className="w-full h-full object-contain"
                                onError={(e) => {
                                  e.currentTarget.src = "/placeholder.svg"
                                }}
                              />
                            </div>
                            <div>
                              <p className="font-medium text-sm">
                                {car.brand} {car.model || ''}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {car.year || 'N/A'} • {car.plateNumber}
                              </p>
                            </div>
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="text-xs space-y-1">
                            <div className="flex items-center gap-1">
                              <span className="text-muted-foreground">VIN:</span>
                              <span className="truncate max-w-[100px]">{car.vin || 'N/A'}</span>
                            </div>
                            {car.notes && (
                              <div className="text-muted-foreground truncate max-w-[120px]">
                                {car.notes}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="p-3">
                          <Badge className={`${getCarStatusColor(car.status || 'active')} text-xs`}>
                            {car.status || 'active'}
                          </Badge>
                        </td>
                        <td className="p-3">
                          <span className="text-xs">
                            {car.registrationDate ? new Date(car.registrationDate).toLocaleDateString() : 'N/A'}
                          </span>
                        </td>
                        <td className="p-3">
                          <div className="flex gap-1">
                            <EditCarDialog car={car}>
                              <Button variant="outline" size="sm" className="h-7 w-7 p-0">
                                <Edit className="h-3 w-3" />
                              </Button>
                            </EditCarDialog>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-7 w-7 p-0"
                              onClick={() => openDeleteDialog(car)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Car</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete the car "{carToDelete?.brand} {carToDelete?.model}" with plate number "{carToDelete?.plateNumber}"?
                This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={deleting}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteCar}
                disabled={deleting}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {deleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Delete Car
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  )
}

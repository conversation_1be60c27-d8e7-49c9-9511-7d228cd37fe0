// Car data types and API functions

export interface Car {
  id: number
  plateNumber: string
  brand: string
  model?: string
  year?: number
  vin?: string
  registrationDate?: string
  status?: 'active' | 'inactive' | 'maintenance' | 'sold'
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface CreateCarData {
  plateNumber: string
  brand: string
  model?: string
  year?: number
  vin?: string
  registrationDate?: string
  status?: 'active' | 'inactive' | 'maintenance' | 'sold'
  notes?: string
}

export interface UpdateCarData extends Partial<CreateCarData> {}

export interface CarsResponse {
  success: boolean
  data: {
    cars: Car[]
    pagination: {
      total: number
      limit: number
      offset: number
      hasMore: boolean
    }
  }
}

export interface CarResponse {
  success: boolean
  data: Car
}

export interface CarFilters {
  status?: string
  search?: string
  brand?: string
  limit?: number
  offset?: number
}

// API Functions
export class CarsAPI {
  private static baseUrl = '/api/cars'

  // Fetch all cars with optional filters
  static async getCars(filters: CarFilters = {}): Promise<CarsResponse> {
    const params = new URLSearchParams()
    
    if (filters.status) params.append('status', filters.status)
    if (filters.search) params.append('search', filters.search)
    if (filters.brand) params.append('brand', filters.brand)
    if (filters.limit) params.append('limit', filters.limit.toString())
    if (filters.offset) params.append('offset', filters.offset.toString())

    const url = `${this.baseUrl}?${params.toString()}`
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch cars: ${response.statusText}`)
    }

    return response.json()
  }

  // Fetch a specific car by ID
  static async getCar(id: number): Promise<CarResponse> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch car: ${response.statusText}`)
    }

    return response.json()
  }

  // Create a new car
  static async createCar(carData: CreateCarData): Promise<CarResponse> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(carData),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `Failed to create car: ${response.statusText}`)
    }

    return response.json()
  }

  // Update an existing car
  static async updateCar(id: number, carData: UpdateCarData): Promise<CarResponse> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(carData),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `Failed to update car: ${response.statusText}`)
    }

    return response.json()
  }

  // Delete a car
  static async deleteCar(id: number): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `Failed to delete car: ${response.statusText}`)
    }

    return response.json()
  }
}

// Helper functions for car data
export const getCarDisplayName = (car: Car): string => {
  return `${car.brand}${car.model ? ` ${car.model}` : ''}`
}

export const getCarStatusColor = (status: string): string => {
  switch (status?.toLowerCase()) {
    case 'active':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'sold':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'maintenance':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'inactive':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

// Car status options for forms
export const CAR_STATUS_OPTIONS = [
  { value: 'active', label: 'Active', labelSq: 'Aktiv' },
  { value: 'sold', label: 'Sold', labelSq: 'I shitur' },
  { value: 'maintenance', label: 'Maintenance', labelSq: 'Në mirëmbajtje' },
  { value: 'inactive', label: 'Inactive', labelSq: 'Jo aktiv' },
] as const

// Popular car brands for forms
export const CAR_BRANDS = [
  'BMW', 'Mercedes', 'Audi', 'Toyota', 'Volkswagen', 'Ford', 
  'Tesla', 'Porsche', 'Lamborghini', 'Ferrari', 'Bentley', 'Rolls-Royce'
] as const

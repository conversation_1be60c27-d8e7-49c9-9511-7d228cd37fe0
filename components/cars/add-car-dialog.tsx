"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Loader2, Car, Calendar, Hash, FileText } from "lucide-react"
import { useCars } from "@/hooks/use-cars"
import { CreateCarData, CAR_STATUS_OPTIONS, CAR_BRANDS } from "@/lib/cars"

interface AddCarDialogProps {
  children: React.ReactNode
}

interface CarFormData {
  plateNumber: string
  brand: string
  model: string
  year: string
  vin: string
  registrationDate: string
  status: 'active' | 'inactive' | 'maintenance' | 'sold'
  notes: string
}

export function AddCarDialog({ children }: AddCarDialogProps) {
  const { createCar } = useCars()
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Form state
  const [formData, setFormData] = useState<CarFormData>({
    plateNumber: "",
    brand: "",
    model: "",
    year: "",
    vin: "",
    registrationDate: "",
    status: "active",
    notes: ""
  })

  const handleInputChange = (field: keyof CarFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const resetForm = () => {
    setFormData({
      plateNumber: "",
      brand: "",
      model: "",
      year: "",
      vin: "",
      registrationDate: "",
      status: "active",
      notes: ""
    })
    setError(null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.plateNumber.trim()) {
      setError("Plate number is required")
      return
    }
    
    if (!formData.brand.trim()) {
      setError("Brand is required")
      return
    }

    try {
      setLoading(true)
      setError(null)

      const carData: CreateCarData = {
        plateNumber: formData.plateNumber.trim(),
        brand: formData.brand.trim(),
        model: formData.model.trim() || undefined,
        year: formData.year ? parseInt(formData.year) : undefined,
        vin: formData.vin.trim() || undefined,
        registrationDate: formData.registrationDate || undefined,
        status: formData.status,
        notes: formData.notes.trim() || undefined
      }

      await createCar(carData)

      // Success - close dialog and reset form
      setOpen(false)
      resetForm()
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create car')
    } finally {
      setLoading(false)
    }
  }

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen)
    if (!newOpen) {
      resetForm()
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto mx-4 sm:mx-0">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Car className="h-5 w-5" />
            Add New Car
          </DialogTitle>
          <DialogDescription>
            Add a new car to your rental fleet. Fill in the required information below.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="bg-destructive/10 border border-destructive/20 text-destructive px-3 py-2 rounded-md text-sm">
              {error}
            </div>
          )}

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Plate Number */}
            <div className="space-y-2">
              <Label htmlFor="plateNumber" className="flex items-center gap-1">
                <Hash className="h-4 w-4" />
                Plate Number *
              </Label>
              <Input
                id="plateNumber"
                value={formData.plateNumber}
                onChange={(e) => handleInputChange('plateNumber', e.target.value)}
                placeholder="e.g., ABC-123"
                required
                className="uppercase"
                maxLength={20}
              />
            </div>

            {/* Brand */}
            <div className="space-y-2">
              <Label htmlFor="brand">Brand *</Label>
              <Select value={formData.brand} onValueChange={(value) => handleInputChange('brand', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select brand" />
                </SelectTrigger>
                <SelectContent>
                  {CAR_BRANDS.map((brand) => (
                    <SelectItem key={brand} value={brand}>
                      {brand}
                    </SelectItem>
                  ))}
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
              {formData.brand === "Other" && (
                <Input
                  value=""
                  onChange={(e) => handleInputChange('brand', e.target.value)}
                  placeholder="Enter custom brand"
                  className="mt-2"
                />
              )}
            </div>

            {/* Model */}
            <div className="space-y-2">
              <Label htmlFor="model">Model</Label>
              <Input
                id="model"
                value={formData.model}
                onChange={(e) => handleInputChange('model', e.target.value)}
                placeholder="e.g., X5, C-Class"
              />
            </div>

            {/* Year */}
            <div className="space-y-2">
              <Label htmlFor="year">Year</Label>
              <Input
                id="year"
                type="number"
                value={formData.year}
                onChange={(e) => handleInputChange('year', e.target.value)}
                placeholder="e.g., 2023"
                min="1900"
                max={new Date().getFullYear() + 1}
              />
            </div>

            {/* VIN */}
            <div className="space-y-2">
              <Label htmlFor="vin">VIN</Label>
              <Input
                id="vin"
                value={formData.vin}
                onChange={(e) => handleInputChange('vin', e.target.value)}
                placeholder="Vehicle Identification Number"
                className="uppercase"
                maxLength={17}
              />
            </div>

            {/* Registration Date */}
            <div className="space-y-2">
              <Label htmlFor="registrationDate" className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                Registration Date
              </Label>
              <Input
                id="registrationDate"
                type="date"
                value={formData.registrationDate}
                onChange={(e) => handleInputChange('registrationDate', e.target.value)}
              />
            </div>
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label>Status</Label>
            <Select value={formData.status} onValueChange={(value: any) => handleInputChange('status', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {CAR_STATUS_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes" className="flex items-center gap-1">
              <FileText className="h-4 w-4" />
              Notes
            </Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes about the car..."
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Add Car
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

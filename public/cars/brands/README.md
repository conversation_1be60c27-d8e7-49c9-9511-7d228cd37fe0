# Car Brand Logos

This directory contains car company logos scraped from [iconduck.com](https://iconduck.com/sets/car-company-logos).

## Structure

```
public/cars/brands/
├── dark/          # Dark/original logos (56 brands)
├── white/         # White/inverted logos (56 brands)
└── README.md      # This file
```

## Available Brands

The following car brands are available in both dark and white versions:

- **Acura** - acura.png
- **Alfa Romeo** - alfa-romeo.png
- **Aston Martin** - aston-martin.png
- **Audi** - audi.png
- **Bentley** - bentley.png
- **BMW** - bmw.png
- **Bugatti** - bugatti.png
- **Buick** - buick.png
- **Cadillac** - cadillac.png
- **Chevrolet** - chevrolet.png
- **Chrysler** - chrysler.png
- **Citroen** - citroen.png
- **Dacia** - dacia.png
- **Daewoo** - daewoo.png
- **Dodge** - dodge.png
- **Ferrari** - ferrari.png
- **Fiat** - fiat.png
- **Ford** - ford.png
- **Genesis** - genesis.png
- **GMC** - gmc.png
- **Honda** - honda.png
- **Hummer** - hummer.png
- **Hyundai** - hyundai.png
- **Infiniti** - infiniti.png
- **Isuzu** - isuzu.png
- **Jaguar** - jaguar.png
- **Jeep** - jeep.png
- **Kia** - kia.png
- **Lamborghini** - lamborghini.png
- **Land Rover** - land-rover.png
- **Lexus** - lexus.png
- **Lincoln** - lincoln.png
- **Lotus** - lotus.png
- **Maserati** - maserati.png
- **Maybach** - maybach.png
- **Mazda** - mazda.png
- **McLaren** - mclaren.png
- **Mercedes-Benz** - mercedes-benz.png
- **Mini** - mini.png
- **Mitsubishi** - mitsubishi.png
- **Nissan** - nissan.png
- **Opel** - opel.png
- **Peugeot** - peugeot.png
- **Pontiac** - pontiac.png
- **Porsche** - porsche.png
- **Ram** - ram.png
- **Renault** - renault.png
- **Rolls-Royce** - rolls-royce.png
- **Saab** - saab.png
- **Seat** - seat.png
- **Skoda** - skoda.png
- **Smart** - smart.png
- **Subaru** - subaru.png
- **Suzuki** - suzuki.png
- **Tesla** - tesla.png
- **Toyota** - toyota.png
- **Volkswagen** - volkswagen.png
- **Volvo** - volvo.png

## Usage

### In React Components

Use the helper function from `lib/cars.ts`:

```typescript
import { getCarBrandLogo } from '@/lib/cars'

// Get dark logo (default)
const darkLogo = getCarBrandLogo('BMW')
// Returns: "/cars/brands/dark/bmw.png"

// Get white logo
const whiteLogo = getCarBrandLogo('BMW', 'white')
// Returns: "/cars/brands/white/bmw.png"
```

### Direct URLs

```
Dark logos:  /cars/brands/dark/{brand-name}.png
White logos: /cars/brands/white/{brand-name}.png
```

## Brand Name Normalization

Brand names are automatically normalized:
- Converted to lowercase
- Spaces replaced with hyphens
- Special mappings:
  - "Mercedes" → "mercedes-benz"
  - "Land Rover" → "land-rover"
  - "Alfa Romeo" → "alfa-romeo"
  - "Aston Martin" → "aston-martin"
  - "Rolls-Royce" → "rolls-royce"

## License

These logos are from the "Car Company Logos" icon set by Dan G. Nelson, licensed under MIT License.
Source: https://iconduck.com/sets/car-company-logos

## Notes

- All logos are in PNG format
- Original resolution: 256x256 (or proportional)
- White versions are currently copies of originals (ImageMagick not available during setup)
- Fallback to `/placeholder.svg` if logo not found

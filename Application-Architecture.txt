Database Architecture
PostgreSQL Database: Running in Docker container with configuration in  docker-compose.yml
Connection Management: Implemented in  lib/database.ts with connection pooling
Environment Configuration: Database credentials stored in  .env files (following  .env.example pattern)
Docker Setup:
PostgreSQL container exposed on port 5432
pgAdmin web interface on port 5050
Persistent volumes for data storage
Custom network configuration for service isolation
UI Framework & Component Structure
Component Library: ShadCN UI with custom theming
CSS Framework: Tailwind CSS with custom configuration in  tailwind.config.ts
Component Organization:
/components/ui/: Base UI components (buttons, inputs, etc.)
/components/: Application-specific components
Atomic design principles with composition patterns
Layout System:
Root layout in  app/layout.tsx providing theme and language context
Dashboard layout in  app/dashboard/layout.tsx with sidebar integration
Responsive container system with mobile breakpoints
Internationalization System
Translation Management:
Dual language support (English/Albanian) via LanguageProvider
Translation keys organized by feature area in nested objects
Client-side language persistence using localStorage
Translation Usage:
useLanguage() hook providing t() function for text translation
Language toggle component for user switching
Default language detection based on browser settings
Device & Responsive Support
Responsive Strategy:
Mobile-first design approach with Tailwind breakpoints
Custom hook useIsMobile() for conditional rendering
Media query listeners for dynamic adaptation
Device Optimization:
Image optimization configured in Next.js
Lazy loading components for performance
Conditional rendering based on device capabilities
Theme System
Theme Implementation:
Next-themes integration via ThemeProvider
CSS variables for theme tokens in globals.css
System preference detection with manual override
Theme Components:
ThemeToggle component for user switching
Dark/light specific styling with Tailwind's dark: modifier
Consistent theming across all UI components
Application Framework
Next.js Configuration:
App Router architecture with file-based routing
Server Components for improved performance
Client Components for interactive elements
Middleware for request processing
State Management:
React Context for global state (theme, language)
Local component state for UI interactions
Server state with data fetching patterns
Authentication & Security
Auth Implementation:
NextAuth.js integration (referenced in environment variables)
Protected routes with middleware
User session management
Security Measures:
Environment variable protection
Server-side validation
CSRF protection
Code Organization
Project Structure:
/app/: Routes and page components
/components/: Reusable UI components
/lib/: Utility functions and services
/public/: Static assets
TypeScript Configuration:
Strict type checking
Path aliases for clean imports
Type definitions for components and data
Development Workflow
Environment Setup:
Docker for development dependencies
NPM for package management
Environment variable templates
Build Process:
Next.js build optimization
TypeScript compilation
Asset optimization
This architecture follows modern web development best practices with a focus on performance, maintainability, and developer experience. The separation of concerns and modular approach allows for scalable development and easy feature additions.